// 纯白极简质感风格主题样式
// 参考现代极简设计理念

// 强制禁用夜间模式
html,
body {
  color-scheme: light !important;
}

html.dark,
body.dark,
.dark {
  color-scheme: light !important;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%) !important;
}

// 颜色变量
:root,
html,
body,
html.dark,
body.dark,
.dark {
  // 主色调 - 质感蓝色
  --primary-color: #1890ff;
  --primary-hover: #40a9ff;
  --primary-light: rgba(24, 144, 255, 0.05);
  --primary-border: rgba(24, 144, 255, 0.3);

  // 文字颜色
  --text-primary: #262626;
  --text-secondary: #595959;
  --text-tertiary: #8c8c8c;
  --text-disabled: #bfbfbf;

  // 背景颜色 - 透明质感
  --bg-primary: rgba(255, 255, 255, 0.95);
  --bg-secondary: rgba(255, 255, 255, 0.8);
  --bg-tertiary: rgba(250, 250, 250, 0.6);
  --bg-hover: rgba(0, 0, 0, 0.02);
  --bg-glass: rgba(255, 255, 255, 0.7);

  // 边框颜色
  --border-primary: rgba(0, 0, 0, 0.06);
  --border-secondary: rgba(0, 0, 0, 0.1);
  --border-light: rgba(0, 0, 0, 0.15);
  --border-focus: var(--primary-color);

  // 状态颜色
  --success-color: #52c41a;
  --success-bg: rgba(82, 196, 26, 0.05);
  --warning-color: #faad14;
  --warning-bg: rgba(250, 173, 20, 0.05);
  --danger-color: #ff4d4f;
  --danger-bg: rgba(255, 77, 79, 0.05);

  // 阴影 - 质感阴影
  --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.06);
  --shadow-medium: 0 4px 12px rgba(0, 0, 0, 0.1);
  --shadow-card: 0 2px 8px rgba(0, 0, 0, 0.08);
  --shadow-glass: 0 8px 32px rgba(0, 0, 0, 0.12);

  // 圆角
  --radius-small: 6px;
  --radius-medium: 8px;
  --radius-large: 12px;

  // 字体
  --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  --font-size-xs: 11px;
  --font-size-sm: 12px;
  --font-size-base: 14px;
  --font-size-md: 15px;
  --font-size-lg: 16px;
  --font-size-xl: 18px;

  // 间距
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 12px;
  --spacing-lg: 16px;
  --spacing-xl: 24px;
}

// 全局样式重置
* {
  box-sizing: border-box;
}

body {
  font-family: var(--font-family);
  color: var(--text-primary);
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  margin: 0;
  padding: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// 毛玻璃效果工具类
.glass {
  background: var(--bg-glass);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-primary);
}

.glass-light {
  background: var(--bg-secondary);
  backdrop-filter: blur(10px);
  border: 1px solid var(--border-primary);
}

// 悬浮效果工具类
.hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
  }
}

// Element Plus 组件样式强制覆盖
.el-button,
.ep-button {
  border-radius: var(--radius-small) !important;
  font-size: var(--font-size-base) !important;
  font-weight: 400 !important;
  background: transparent !important;
  backdrop-filter: blur(10px) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;

  &.el-button--primary,
  &.ep-button--primary {
    background: transparent !important;
    border: 1px solid var(--primary-color) !important;
    color: var(--primary-color) !important;

    &:hover,
    &:focus {
      background: var(--primary-light) !important;
      border-color: var(--primary-hover) !important;
      color: var(--primary-hover) !important;
      transform: translateY(-1px) !important;
      box-shadow: var(--shadow-medium) !important;
    }

    &:active {
      transform: translateY(0) !important;
    }
  }

  &.el-button--default,
  &.ep-button--default {
    background: transparent !important;
    border: 1px solid var(--border-primary) !important;
    color: var(--text-secondary) !important;

    &:hover,
    &:focus {
      border-color: var(--primary-border) !important;
      color: var(--primary-color) !important;
      background: var(--bg-hover) !important;
      box-shadow: var(--shadow-light) !important;
    }
  }

  &.el-button--text,
  &.el-button--link,
  &.ep-button--text,
  &.ep-button--link {
    background: transparent !important;
    border: none !important;
    color: var(--primary-color) !important;

    &:hover,
    &:focus {
      background: var(--primary-light) !important;
      color: var(--primary-hover) !important;
    }
  }

  &.el-button--large,
  &.ep-button--large {
    padding: 12px 20px !important;
    font-size: var(--font-size-md) !important;
  }

  &.el-button--small,
  &.ep-button--small {
    padding: 6px 12px !important;
    font-size: var(--font-size-sm) !important;
  }
}

.el-input {
  .el-input__wrapper {
    border-radius: var(--radius-small);
    border: 1px solid var(--border-primary);
    box-shadow: none;
    background: var(--bg-glass);
    backdrop-filter: blur(10px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    &:hover {
      border-color: var(--border-secondary);
      background: var(--bg-primary);
    }

    &.is-focus {
      border-color: var(--border-focus);
      background: var(--bg-primary);
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
    }
  }

  .el-input__inner {
    font-size: var(--font-size-base);
    color: var(--text-primary);
    background: transparent;

    &::placeholder {
      color: var(--text-tertiary);
    }
  }

  &.el-input--large {
    .el-input__wrapper {
      padding: 12px 16px;
    }

    .el-input__inner {
      font-size: var(--font-size-md);
    }
  }
}

.el-input-number {
  .el-input__wrapper {
    border-radius: var(--radius-small);
    border: 1px solid var(--border-primary);
    background: var(--bg-glass);
    backdrop-filter: blur(10px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    &:hover {
      border-color: var(--border-secondary);
      background: var(--bg-primary);
    }

    &.is-focus {
      border-color: var(--border-focus);
      background: var(--bg-primary);
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
    }
  }
}

.el-select {
  .el-input__wrapper {
    border-radius: var(--radius-small);
    border: 1px solid var(--border-primary);
    background: var(--bg-glass);
    backdrop-filter: blur(10px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    &:hover {
      border-color: var(--border-secondary);
      background: var(--bg-primary);
    }

    &.is-focus {
      border-color: var(--border-focus);
      background: var(--bg-primary);
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
    }
  }
}

.el-card {
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-medium);
  box-shadow: var(--shadow-card);
  background: var(--bg-glass);
  backdrop-filter: blur(20px);

  .el-card__header {
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-primary);
    padding: var(--spacing-lg);
    backdrop-filter: blur(10px);

    .card-header {
      font-size: var(--font-size-md);
      font-weight: 500;
      color: var(--text-primary);
    }
  }

  .el-card__body {
    padding: var(--spacing-lg);
    background: transparent;
  }
}

.el-dialog {
  border-radius: var(--radius-large);
  box-shadow: var(--shadow-glass);
  background: var(--bg-glass);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-primary);

  .el-dialog__header {
    padding: var(--spacing-xl) var(--spacing-xl) var(--spacing-lg);
    border-bottom: 1px solid var(--border-primary);
    background: transparent;

    .el-dialog__title {
      font-size: var(--font-size-lg);
      font-weight: 500;
      color: var(--text-primary);
    }
  }

  .el-dialog__body {
    padding: var(--spacing-xl);
    background: transparent;
  }
}

.el-tag {
  border-radius: 2px;
  font-size: var(--font-size-xs);
  height: 18px;
  line-height: 16px;
  padding: 0 6px;
  border: none;
  font-weight: 400;
  
  &.el-tag--success {
    background: var(--success-bg);
    color: var(--success-color);
  }
  
  &.el-tag--danger {
    background: var(--danger-bg);
    color: var(--danger-color);
  }
  
  &.el-tag--warning {
    background: var(--warning-bg);
    color: var(--warning-color);
  }
}

.el-tabs {
  .el-tabs__header {
    margin: 0;
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-secondary);
  }
  
  .el-tabs__nav-wrap::after {
    display: none;
  }
  
  .el-tabs__item {
    color: var(--text-secondary);
    font-size: var(--font-size-base);
    font-weight: 400;
    border: none;
    
    &.is-active {
      color: var(--primary-color);
      font-weight: 500;
    }
    
    &:hover {
      color: var(--primary-color);
    }
  }
  
  .el-tabs__active-bar {
    background: var(--primary-color);
    height: 2px;
  }
}

.el-form {
  .el-form-item__label {
    color: var(--text-primary);
    font-size: var(--font-size-base);
    font-weight: 400;
  }
}

.el-empty {
  .el-empty__description {
    color: var(--text-secondary);
    font-size: var(--font-size-base);
  }
}

.el-switch,
.ep-switch {
  .el-switch__core,
  .ep-switch__core {
    border: 1px solid var(--border-light) !important;
    background: var(--bg-glass) !important;
    backdrop-filter: blur(10px) !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;

    &.is-checked {
      background: var(--primary-light) !important;
      border-color: var(--primary-color) !important;
    }
  }

  .el-switch__action,
  .ep-switch__action {
    background: var(--bg-primary) !important;
    box-shadow: var(--shadow-light) !important;
    border: 1px solid var(--border-primary) !important;
  }
}

// 强制覆盖所有可能的绿色样式
.el-button--primary,
.ep-button--primary {
  --el-button-bg-color: transparent !important;
  --el-button-border-color: var(--primary-color) !important;
  --el-button-text-color: var(--primary-color) !important;
  --el-button-hover-bg-color: var(--primary-light) !important;
  --el-button-hover-border-color: var(--primary-hover) !important;
  --el-button-hover-text-color: var(--primary-hover) !important;
}

.el-switch.is-checked .el-switch__core,
.ep-switch.is-checked .ep-switch__core {
  background-color: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
}

// 覆盖CSS变量
:root {
  --el-color-primary: #1890ff !important;
  --el-color-primary-light-1: #40a9ff !important;
  --el-color-primary-light-2: #69c0ff !important;
  --el-color-primary-light-3: #91d5ff !important;
  --el-color-primary-light-4: #b7e7ff !important;
  --el-color-primary-light-5: #d6f7ff !important;
  --el-color-primary-light-6: #e6f7ff !important;
  --el-color-primary-light-7: #f0f9ff !important;
  --el-color-primary-light-8: #f7fcff !important;
  --el-color-primary-light-9: #fafcff !important;
  --el-color-primary-dark-1: #1677cc !important;
  --el-color-primary-dark-2: #135aa3 !important;

  // 强制覆盖任何绿色相关的变量
  --el-color-success: #52c41a !important;
  --el-color-success-light-3: rgba(82, 196, 26, 0.3) !important;
  --el-color-success-light-5: rgba(82, 196, 26, 0.5) !important;
  --el-color-success-light-7: rgba(82, 196, 26, 0.7) !important;
  --el-color-success-light-8: rgba(82, 196, 26, 0.8) !important;
  --el-color-success-light-9: rgba(82, 196, 26, 0.9) !important;

  // 确保没有绿色残留
  --el-color-info: #1890ff !important;
}

// 强制覆盖所有可能的绿色背景
* {
  &[style*="background-color: green"],
  &[style*="background-color: #00b96b"],
  &[style*="background-color: #07c160"],
  &[style*="background: green"],
  &[style*="background: #00b96b"],
  &[style*="background: #07c160"] {
    background-color: var(--primary-color) !important;
    background: var(--primary-color) !important;
  }

  &[style*="border-color: green"],
  &[style*="border-color: #00b96b"],
  &[style*="border-color: #07c160"] {
    border-color: var(--primary-color) !important;
  }

  &[style*="color: green"],
  &[style*="color: #00b96b"],
  &[style*="color: #07c160"] {
    color: var(--primary-color) !important;
  }
}

.el-result {
  .el-result__title {
    color: var(--text-primary);
    font-size: var(--font-size-lg);
    font-weight: 400;
  }

  .el-result__subtitle {
    color: var(--text-secondary);
    font-size: var(--font-size-base);
  }
}

// 滚动条样式
::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: var(--border-light);
  border-radius: 2px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-disabled);
}

// 工具类
.text-primary {
  color: var(--text-primary);
}

.text-secondary {
  color: var(--text-secondary);
}

.text-disabled {
  color: var(--text-disabled);
}

.bg-primary {
  background: var(--bg-primary);
}

.bg-secondary {
  background: var(--bg-secondary);
}

.border-light {
  border: 1px solid var(--border-light);
}

.border-secondary {
  border: 1px solid var(--border-secondary);
}

.radius-small {
  border-radius: var(--radius-small);
}

.radius-medium {
  border-radius: var(--radius-medium);
}

.radius-large {
  border-radius: var(--radius-large);
}

{"compilerOptions": {"target": "esnext", "jsx": "preserve", "lib": ["esnext", "dom"], "useDefineForClassFields": true, "baseUrl": ".", "module": "esnext", "moduleResolution": "bundler", "paths": {"~/*": ["src/*"], "@/*": ["src/*"]}, "resolveJsonModule": true, "types": ["vite/client", "unplugin-vue-router/client"], "strict": true, "sourceMap": true, "esModuleInterop": true, "skipLibCheck": true}, "vueCompilerOptions": {"target": 3}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue"]}